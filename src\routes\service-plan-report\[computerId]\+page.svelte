<script>
  import { goto } from '$app/navigation';
  import * as XLSX from 'xlsx';

  /** @type {import('./$types').PageData} */
  export let data;

  $: servicePlan = data.servicePlan;
  $: computer = servicePlan.computer;
  $: workloadSummary = servicePlan.workloadSummary;
  $: servicesByMonth = servicePlan.servicePlan.servicesByMonth;
  $: statistics = servicePlan.servicePlan.statistics;

  // State for disabled services (customer doing themselves)
  let disabledServices = new Set(data.disabledServices || []);

  // Create unique identifier for each service
  function getServiceId(service, monthData, targetHours) {
    return `${service.serviceCode}-${service.actionType}-${service.partNumber || 'no-part'}-${targetHours}-${monthData.year}-${monthData.month}-${service.activityPurpose || service.serviceActivityLabel || 'default'}`;
  }

  // Toggle service disabled state
  async function toggleServiceDisabled(serviceId) {
    if (disabledServices.has(serviceId)) {
      disabledServices.delete(serviceId);
    } else {
      disabledServices.add(serviceId);
    }
    disabledServices = disabledServices; // Trigger reactivity

    // Save to database
    await saveDisabledServices();
  }

  // Save disabled services to database
  async function saveDisabledServices() {
    try {
      const response = await fetch(`/api/computer-service-plan?computerId=${data.computerId}&productDesignation=${data.productDesignation}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          disabledServices: Array.from(disabledServices)
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to save disabled services: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Disabled services saved successfully:', result);
    } catch (error) {
      console.error('❌ Error saving disabled services:', error);
      // You could add a toast notification here
    }
  }

  // Format date for display
  function formatDate(dateStr) {
    return new Date(dateStr).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  // Format month/year
  function formatMonthYear(year, month) {
    return new Date(year, month - 1).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    });
  }

  // Print function
  function printReport() {
    window.print();
  }

  // Go back to computer details
  function goBack() {
    goto(`/computer-id/${data.computerId}`);
  }

  // Group services by target hours to show connected services
  function groupServicesByHours(services) {
    const grouped = {};
    services.forEach(service => {
      const targetHours = service.targetHours;
      if (!grouped[targetHours]) {
        grouped[targetHours] = [];
      }
      grouped[targetHours].push(service);
    });

    // Sort by target hours
    const sortedKeys = Object.keys(grouped).sort((a, b) => parseInt(a) - parseInt(b));
    const sortedGrouped = {};
    sortedKeys.forEach(key => {
      sortedGrouped[key] = grouped[key];
    });

    return sortedGrouped;
  }

  // Regenerate service plan
  async function regenerateServicePlan() {
    try {
      const url = `/api/service-plan-generator/${data.computerId}?productDesignation=${data.productDesignation}`;
      window.location.reload();
    } catch (error) {
      console.error('Error regenerating service plan:', error);
      alert('Failed to regenerate service plan. Please try again.');
    }
  }

  // Save service plan to database
  async function saveServicePlan() {
    try {
      const response = await fetch('/api/computer-service-plan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          computerId: data.computerId,
          productDesignation: data.productDesignation,
          servicePlanData: servicePlan
        })
      });

      const result = await response.json();

      if (result.success) {
        alert(`Service plan ${result.action} successfully!`);
      } else {
        alert(`Failed to save service plan: ${result.error}`);
      }
    } catch (error) {
      console.error('Error saving service plan:', error);
      alert('Failed to save service plan. Please try again.');
    }
  }

  // Export service plan to Excel
  function exportToExcel() {
    try {
      // Create a new workbook
      const workbook = XLSX.utils.book_new();

      // Sheet 1: Computer Information
      const computerInfo = [
        ['Computer Information', ''],
        ['Computer ID', computer._id],
        ['Name', computer.name],
        ['Product Designation', computer.productDesignation],
        ['Serial Number', computer.serialNumber],
        ['Model', computer.model],
        ['', ''],
        ['Workload Summary', ''],
        ['Total Workload Entries', workloadSummary.totalEntries],
        ['Total Accumulated Hours', workloadSummary.totalAccumulatedHours],
        ['', ''],
        ['Service Plan Statistics', ''],
        ['Total Scheduled Services', statistics.totalServices],
        ['Service Types', statistics.serviceTypes],
        ['Total Months Covered', statistics.timeSpan.totalMonths],
        ['Average Monthly Hours', Math.round(statistics.utilizationStats.averageMonthlyHours)],
        ['Peak Month Hours', statistics.utilizationStats.peakMonth.monthHours],
        ['Peak Month', `${statistics.utilizationStats.peakMonth.year}/${statistics.utilizationStats.peakMonth.month}`],
        ['', ''],
        ['Report Information', ''],
        ['Generated At', formatDate(servicePlan.generatedAt)],
        ['Data Source', servicePlan.servicePlan.dataSource]
      ];

      const computerSheet = XLSX.utils.aoa_to_sheet(computerInfo);

      // Set column widths for computer info sheet
      computerSheet['!cols'] = [
        { width: 25 },
        { width: 30 }
      ];

      XLSX.utils.book_append_sheet(workbook, computerSheet, 'Computer Info');

      // Sheet 2: Service Schedule
      const serviceHeaders = [
        'Month/Year',
        'Accumulated Hours',
        'Target Hours',
        'Service Code',
        'Action Type',
        'Activity Purpose',
        'Part Number',
        'Quantity',
        'Frequency (Hours)',
        'Exact Match',
        'Sequence Number',
        'Labour Time (VST Hours)',
        'VST Code',
        'Labour Description',
        'Service Phase'
      ];

      const serviceData = [serviceHeaders];

      // Add service data
      servicesByMonth.forEach(monthData => {
        const monthYear = formatMonthYear(monthData.year, monthData.month);
        const accHours = monthData.accumulatedHours;

        monthData.services.forEach(service => {
          serviceData.push([
            monthYear,
            accHours,
            service.targetHours,
            service.serviceCode,
            service.actionType,
            service.activityPurpose || service.serviceActivityLabel || '',
            service.partNumber || '',
            service.quantity || 1,
            service.frequency,
            service.exactMatch ? 'Yes' : 'No',
            service.sequenceNumber || '',
            service.labourTime?.vstHours || 0,
            service.labourTime?.vstCode || '',
            service.labourTime?.serviceDescription || '',
            service.labourTime?.servicePhase || ''
          ]);
        });
      });

      const serviceSheet = XLSX.utils.aoa_to_sheet(serviceData);

      // Set column widths for service sheet
      serviceSheet['!cols'] = [
        { width: 15 }, // Month/Year
        { width: 18 }, // Accumulated Hours
        { width: 15 }, // Target Hours
        { width: 15 }, // Service Code
        { width: 15 }, // Action Type
        { width: 30 }, // Activity Purpose
        { width: 20 }, // Part Number
        { width: 10 }, // Quantity
        { width: 18 }, // Frequency
        { width: 12 }, // Exact Match
        { width: 15 }, // Sequence Number
        { width: 20 }, // Labour Time (VST Hours)
        { width: 15 }, // VST Code
        { width: 35 }, // Labour Description
        { width: 15 }  // Service Phase
      ];

      XLSX.utils.book_append_sheet(workbook, serviceSheet, 'Service Schedule');

      // Sheet 3: Services by Hours (Grouped)
      const groupedHeaders = [
        'Target Hours',
        'Month/Year',
        'Service Count',
        'Service Code',
        'Action Type',
        'Activity Purpose',
        'Part Number',
        'Quantity',
        'Frequency (Hours)',
        'Exact Match',
        'Labour Time (VST Hours)',
        'VST Code',
        'Labour Description'
      ];

      const groupedData = [groupedHeaders];

      // Add grouped service data
      servicesByMonth.forEach(monthData => {
        const monthYear = formatMonthYear(monthData.year, monthData.month);
        const servicesByHours = groupServicesByHours(monthData.services);

        Object.entries(servicesByHours).forEach(([targetHours, servicesAtHours]) => {
          servicesAtHours.forEach((service, index) => {
            groupedData.push([
              parseInt(targetHours),
              monthYear,
              servicesAtHours.length,
              service.serviceCode,
              service.actionType,
              service.activityPurpose || service.serviceActivityLabel || '',
              service.partNumber || '',
              service.quantity || 1,
              service.frequency,
              service.exactMatch ? 'Yes' : 'No',
              service.labourTime?.vstHours || 0,
              service.labourTime?.vstCode || '',
              service.labourTime?.serviceDescription || ''
            ]);
          });
        });
      });

      const groupedSheet = XLSX.utils.aoa_to_sheet(groupedData);

      // Set column widths for grouped sheet
      groupedSheet['!cols'] = [
        { width: 15 }, // Target Hours
        { width: 15 }, // Month/Year
        { width: 15 }, // Service Count
        { width: 15 }, // Service Code
        { width: 15 }, // Action Type
        { width: 30 }, // Activity Purpose
        { width: 20 }, // Part Number
        { width: 10 }, // Quantity
        { width: 18 }, // Frequency
        { width: 12 }, // Exact Match
        { width: 20 }, // Labour Time (VST Hours)
        { width: 15 }, // VST Code
        { width: 35 }  // Labour Description
      ];

      XLSX.utils.book_append_sheet(workbook, groupedSheet, 'Services by Hours');

      // Sheet 4: Monthly Workload Data
      if (workloadSummary.monthlyData && workloadSummary.monthlyData.length > 0) {
        const workloadHeaders = [
          'Year',
          'Month',
          'Month Name',
          'Monthly Hours',
          'Accumulated Hours'
        ];

        const workloadData = [workloadHeaders];

        workloadSummary.monthlyData.forEach(month => {
          workloadData.push([
            month.year,
            month.month,
            month.monthName,
            month.monthHours,
            month.accumulatedHours
          ]);
        });

        const workloadSheet = XLSX.utils.aoa_to_sheet(workloadData);

        // Set column widths for workload sheet
        workloadSheet['!cols'] = [
          { width: 8 },  // Year
          { width: 8 },  // Month
          { width: 15 }, // Month Name
          { width: 15 }, // Monthly Hours
          { width: 18 }  // Accumulated Hours
        ];

        XLSX.utils.book_append_sheet(workbook, workloadSheet, 'Workload Data');
      }

      // Generate filename
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `ServicePlan_${computer.name.replace(/[^a-zA-Z0-9]/g, '_')}_${computer.productDesignation}_${timestamp}.xlsx`;

      // Write and download the file
      XLSX.writeFile(workbook, filename);

      console.log(`✅ Excel file exported: ${filename}`);

    } catch (error) {
      console.error('Error exporting to Excel:', error);
      alert('Failed to export to Excel. Please try again.');
    }
  }
</script>

<svelte:head>
  <title>Service Plan Report - {computer.name}</title>
</svelte:head>

<div class="service-plan-container">
  <!-- Header -->
  <div class="header no-print">
    <button class="back-button" on:click={goBack}>
      ← Back to Computer
    </button>
    <h1>Service Plan Report</h1>
    <div class="header-actions">
      <button class="header-button excel-header-btn" on:click={() => exportToExcel()}>
        📊 Export Excel
      </button>
      <button class="header-button print-button" on:click={printReport}>
        🖨️ Print Report
      </button>
    </div>
  </div>

  <!-- Computer Information -->
  <div class="computer-info-section">
    <h2>Computer Information</h2>
    <div class="info-grid">
      <div class="info-item">
        <span class="label">Computer ID:</span>
        <span class="value">{computer._id}</span>
      </div>
      <div class="info-item">
        <span class="label">Name:</span>
        <span class="value">{computer.name}</span>
      </div>
      <div class="info-item">
        <span class="label">Product Designation:</span>
        <span class="value">{computer.productDesignation}</span>
      </div>
      <div class="info-item">
        <span class="label">Serial Number:</span>
        <span class="value">{computer.serialNumber}</span>
      </div>
      <div class="info-item">
        <span class="label">Model:</span>
        <span class="value">{computer.model}</span>
      </div>
    </div>
  </div>

  <!-- Workload Summary -->
  <div class="workload-summary-section">
    <h2>Workload Summary</h2>
    <div class="summary-stats">
      <div class="stat-card">
        <div class="stat-value">{workloadSummary.totalAccumulatedHours.toLocaleString()}</div>
        <div class="stat-label">Total Hours</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{statistics.totalServices}</div>
        <div class="stat-label">Scheduled Services</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{statistics.serviceTypes}</div>
        <div class="stat-label">Service Types</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{statistics.timeSpan.totalMonths}</div>
        <div class="stat-label">Months Covered</div>
      </div>
    </div>
  </div>

  <!-- Service Schedule by Hours -->
  <div class="service-schedule-section">
    <h2>Service Schedule by Accumulated Hours</h2>

    {#if servicesByMonth.length === 0}
      <div class="no-services">
        <p>No services scheduled based on current workload data.</p>
      </div>
    {:else}
      <div class="services-timeline">
        {#each servicesByMonth as monthData}
          <div class="month-group">
            <div class="month-header">
              <h3>{formatMonthYear(monthData.year, monthData.month)}</h3>
              <div class="month-hours">
                Accumulated Hours: <strong>{monthData.accumulatedHours.toLocaleString()}</strong>
              </div>
            </div>

            <div class="services-list">
              <!-- Group services by target hours -->
              {#each Object.entries(groupServicesByHours(monthData.services)) as [targetHours, servicesAtHours]}
                <div class="service-hour-group">
                  <div class="hour-group-header">
                    <div class="target-hours">
                      <span class="hours-badge">@ {parseInt(targetHours).toLocaleString()} Hours</span>
                      <span class="service-count">{servicesAtHours.length} Service{servicesAtHours.length > 1 ? 's' : ''}</span>
                    </div>
                    {#if servicesAtHours.length > 1}
                      <div class="connection-indicator">
                        <span class="connection-line"></span>
                        <span class="connection-text">Performed Together</span>
                      </div>
                    {/if}
                  </div>

                  <div class="connected-services">
                    {#each servicesAtHours as service, index}
                      {@const serviceId = getServiceId(service, monthData, targetHours)}
                      {@const isDisabled = disabledServices.has(serviceId)}
                      <div class="service-item connected"
                           class:exact-match={service.exactMatch}
                           class:multiple={servicesAtHours.length > 1}
                           class:disabled={isDisabled}>

                        <!-- Checkbox for customer self-service -->
                        <div class="service-checkbox">
                          <label class="checkbox-container">
                            <input
                              type="checkbox"
                              checked={isDisabled}
                              on:change={() => toggleServiceDisabled(serviceId)}
                              title="Check if customer is doing this service themselves"
                            />
                            <span class="checkmark"></span>
                          </label>
                          <span class="checkbox-label">Customer Self-Service</span>
                        </div>

                        {#if servicesAtHours.length > 1}
                          <div class="service-number">{index + 1}</div>
                        {/if}

                        <div class="service-content">
                          <div class="service-header">
                            <div class="service-code">
                              <strong>{service.serviceCode}</strong> - {service.actionType}
                              {#if isDisabled}
                                <span class="disabled-indicator">✓ Customer Handled</span>
                              {/if}
                            </div>
                            <div class="service-badges">
                              {#if service.exactMatch}
                                <span class="exact-badge">Exact Match</span>
                              {/if}
                              <span class="frequency-badge">Every {service.frequency.toLocaleString()}h</span>
                            </div>
                          </div>

                          <div class="service-details">
                            <div class="service-description">
                              {service.activityPurpose || service.serviceActivityLabel || 'Standard Service'}
                            </div>

                            {#if service.partNumber}
                              <div class="service-part">
                                <span class="part-label">Part:</span> {service.partNumber}
                                {#if service.quantity > 1}
                                  <span class="quantity">(Qty: {service.quantity})</span>
                                {/if}
                              </div>
                            {/if}

                            <!-- LabourTime Information -->
                            {#if service.labourTime}
                              <div class="labour-time-info">
                                <div class="labour-time-header">
                                  <span class="labour-time-label">Labour Time:</span>
                                  <span class="labour-time-hours">{service.labourTime.vstHours} VST Hours</span>
                                </div>
                                {#if service.labourTime.serviceDescription}
                                  <div class="labour-time-description">
                                    {service.labourTime.serviceDescription}
                                  </div>
                                {/if}
                                {#if service.labourTime.vstCode}
                                  <div class="labour-time-details">
                                    <span class="vst-code">VST Code: {service.labourTime.vstCode}</span>
                                    {#if service.labourTime.servicePhase}
                                      <span class="service-phase">Phase: {service.labourTime.servicePhase}</span>
                                    {/if}
                                  </div>
                                {/if}
                              </div>
                            {:else}
                              <div class="labour-time-info no-labour-time">
                                <span class="no-labour-time-text">No labour time data available</span>
                              </div>
                            {/if}
                          </div>
                        </div>
                      </div>
                    {/each}
                  </div>
                </div>
              {/each}
            </div>
          </div>
        {/each}
      </div>
    {/if}
  </div>

  <!-- Report Footer -->
  <div class="report-footer">
    <div class="generated-info">
      <p><strong>Report Generated:</strong> {formatDate(servicePlan.generatedAt)}</p>
      <p><strong>Based on:</strong> {workloadSummary.totalEntries} workload entries</p>
      {#if servicePlan.saved}
        <p><strong>Saved to Database:</strong> {formatDate(servicePlan.savedAt)}</p>
      {/if}
      {#if servicePlan.servicePlan.dataSource}
        <p><strong>Data Source:</strong> {servicePlan.servicePlan.dataSource}</p>
      {/if}
    </div>

    <div class="report-actions no-print">
      <button
        class="action-button regenerate-btn"
        on:click={() => regenerateServicePlan()}
      >
        🔄 Regenerate Plan
      </button>

      <button
        class="action-button save-btn"
        on:click={() => saveServicePlan()}
      >
        💾 Save to Database
      </button>

      <button
        class="action-button excel-btn"
        on:click={() => exportToExcel()}
      >
        📊 Export to Excel
      </button>
    </div>
  </div>
</div>

<style>
  .service-plan-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e2e8f0;
  }

  .header h1 {
    margin: 0;
    color: #1e293b;
    font-size: 2rem;
    font-weight: 700;
  }

  .header-actions {
    display: flex;
    gap: 0.75rem;
  }

  .back-button, .header-button {
    padding: 0.5rem 1rem;
    background: #1e40af;
    color: white;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
    font-size: 0.875rem;
  }

  .back-button:hover, .header-button:hover {
    background: #1e3a8a;
  }

  .excel-header-btn {
    background: #16a34a !important;
  }

  .excel-header-btn:hover {
    background: #15803d !important;
  }

  .computer-info-section, .workload-summary-section, .service-schedule-section {
    margin-bottom: 2rem;
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
  }

  .computer-info-section h2, .workload-summary-section h2, .service-schedule-section h2 {
    margin: 0 0 1rem 0;
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 600;
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .value {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
  }

  .summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .stat-card {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    text-align: center;
  }

  .stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }

  .stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .services-timeline {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .month-group {
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    overflow: hidden;
  }

  .month-header {
    background: #f8fafc;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .month-header h3 {
    margin: 0;
    color: #1e293b;
    font-size: 1.25rem;
    font-weight: 600;
  }

  .month-hours {
    color: #64748b;
    font-size: 0.875rem;
  }

  .services-list {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .service-hour-group {
    border: 2px solid #e2e8f0;
    border-radius: 0.75rem;
    overflow: hidden;
    background: white;
  }

  .hour-group-header {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .target-hours {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .hours-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 1.125rem;
  }

  .service-count {
    background: rgba(255, 255, 255, 0.15);
    padding: 0.25rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .connection-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    opacity: 0.9;
  }

  .connection-line {
    width: 2rem;
    height: 2px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 1px;
  }

  .connected-services {
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  .service-item {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    padding: 1rem;
    transition: all 0.2s;
  }

  .service-item.connected {
    background: #fafbfc;
    border: none;
    border-radius: 0;
    border-bottom: 1px solid #e2e8f0;
    padding: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
  }

  .service-item.connected:last-child {
    border-bottom: none;
  }

  .service-item.connected.multiple {
    background: linear-gradient(90deg, #f0f9ff 0%, #fafbfc 100%);
  }

  .service-item.exact-match {
    border-color: #10b981;
    background: #f0fdf4;
  }

  .service-item.connected.exact-match {
    background: linear-gradient(90deg, #f0fdf4 0%, #f0f9ff 100%);
    border-left: 4px solid #10b981;
  }

  .service-item.disabled {
    opacity: 0.6;
    background: #f3f4f6 !important;
    color: #6b7280;
  }

  .service-item.disabled .service-code,
  .service-item.disabled .service-description,
  .service-item.disabled .service-part {
    color: #9ca3af;
  }

  .service-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 0.375rem;
    border: 1px solid #e2e8f0;
  }

  .checkbox-container {
    position: relative;
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .checkbox-container input[type="checkbox"] {
    opacity: 0;
    position: absolute;
    cursor: pointer;
  }

  .checkmark {
    height: 18px;
    width: 18px;
    background-color: #fff;
    border: 2px solid #d1d5db;
    border-radius: 3px;
    position: relative;
    transition: all 0.2s;
  }

  .checkbox-container:hover .checkmark {
    border-color: #3b82f6;
  }

  .checkbox-container input:checked ~ .checkmark {
    background-color: #3b82f6;
    border-color: #3b82f6;
  }

  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }

  .checkbox-container input:checked ~ .checkmark:after {
    display: block;
  }

  .checkbox-label {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .disabled-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    background: #10b981;
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 0.5rem;
  }

  .service-number {
    background: #1e40af;
    color: white;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    flex-shrink: 0;
  }

  .service-content {
    flex: 1;
    min-width: 0;
  }

  .service-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
    gap: 1rem;
  }

  .service-code {
    font-size: 1.125rem;
    color: #1e293b;
    font-weight: 600;
  }

  .service-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    align-items: center;
  }

  .frequency-badge {
    background: #e2e8f0;
    color: #475569;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .service-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .service-description {
    color: #374151;
    font-size: 0.875rem;
  }

  .service-part {
    color: #6b7280;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .part-label {
    font-weight: 600;
    color: #374151;
  }

  .quantity {
    color: #9ca3af;
    font-size: 0.8rem;
  }

  /* LabourTime Information Styling */
  .labour-time-info {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 0.375rem;
    border: 1px solid #bae6fd;
    border-left: 4px solid #0ea5e9;
  }

  .labour-time-info.no-labour-time {
    background: #f9fafb;
    border-color: #e5e7eb;
    border-left-color: #9ca3af;
  }

  .labour-time-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .labour-time-label {
    font-weight: 600;
    color: #0c4a6e;
    font-size: 0.875rem;
  }

  .labour-time-hours {
    background: #0ea5e9;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 600;
    font-size: 0.875rem;
  }

  .labour-time-description {
    color: #374151;
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
    font-style: italic;
  }

  .labour-time-details {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .vst-code {
    background: #ddd6fe;
    color: #5b21b6;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    font-family: 'Courier New', monospace;
  }

  .service-phase {
    background: #fed7aa;
    color: #c2410c;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .no-labour-time-text {
    color: #9ca3af;
    font-size: 0.8rem;
    font-style: italic;
  }

  .exact-badge {
    background: #10b981;
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 500;
  }

  .no-services {
    text-align: center;
    padding: 2rem;
    color: #64748b;
  }

  .report-footer {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;
  }

  .generated-info {
    color: #64748b;
    font-size: 0.875rem;
  }

  .report-actions {
    display: flex;
    gap: 1rem;
    flex-shrink: 0;
  }

  .action-button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .regenerate-btn {
    background: #f59e0b;
    color: white;
  }

  .regenerate-btn:hover {
    background: #d97706;
  }

  .save-btn {
    background: #10b981;
    color: white;
  }

  .save-btn:hover {
    background: #059669;
  }

  .excel-btn {
    background: #16a34a;
    color: white;
  }

  .excel-btn:hover {
    background: #15803d;
  }

  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }

    .service-checkbox {
      display: none !important;
    }

    .service-plan-container {
      padding: 1rem;
    }

    .month-group {
      break-inside: avoid;
      margin-bottom: 1rem;
    }
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .service-plan-container {
      padding: 1rem;
    }

    .header {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;
    }

    .header-actions {
      justify-content: center;
    }

    .info-grid {
      grid-template-columns: 1fr;
    }

    .summary-stats {
      grid-template-columns: repeat(2, 1fr);
    }

    .service-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    .hour-group-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .target-hours {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    .connection-indicator {
      align-self: flex-end;
    }

    .service-item.connected {
      flex-direction: column;
      gap: 1rem;
    }

    .service-number {
      align-self: flex-start;
    }

    .service-badges {
      justify-content: flex-start;
    }

    .report-footer {
      flex-direction: column;
      gap: 1rem;
    }

    .report-actions {
      justify-content: center;
    }
  }
</style>
